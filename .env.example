# Luno Trading Bot Environment Configuration
# Copy this file to .env and fill in your actual values

# Luno API Credentials
LUNO_API_KEY=your_luno_api_key_here
LUNO_API_SECRET=your_luno_api_secret_here

# Trading Configuration
# Supported pairs: XBTMYR, XBTZAR, XBTEUR, XBTGBP, ETHMYR, ETHZAR, ETHXBT, etc.
TRADING_PAIR=XBTMYR
MAX_POSITION_SIZE_PERCENT=2.0
STOP_LOSS_PERCENT=1.5
TAKE_PROFIT_PERCENT=3.0
MAX_DAILY_TRADES=3

# Bot Operation
DRY_RUN=true
CHECK_INTERVAL=60
LOG_LEVEL=INFO

# Dashboard Configuration
DASHBOARD_HOST=127.0.0.1
DASHBOARD_PORT=5000

# Risk Management
ENABLE_STOP_LOSS=true
ENABLE_TAKE_PROFIT=true
MAX_DRAWDOWN_PERCENT=10.0

# Technical Analysis
RSI_PERIOD=14
RSI_OVERSOLD=30
RSI_OVERBOUGHT=70
EMA_SHORT=9
EMA_LONG=21
BOLLINGER_PERIOD=20
BOLLINGER_STD=2.0

# Trading Hours (24/7 for crypto, but can be restricted)
TRADING_HOURS_START=8
TRADING_HOURS_END=22
TIMEZONE=Asia/Kuala_Lumpur